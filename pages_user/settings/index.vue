<!--
 * @Description: 设置页面 - 语言切换和缓存管理
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02
 * @Layout: 简洁设置页面，包含语言切换和清理缓存功能
-->
<template>
  <view class="user-page-container">
    <!-- 语言设置 -->
    <view class="user-card">
      <view class="card-title">语言设置</view>
      <view class="settings-list">
        <view class="setting-item" @click="showLanguageSelector">
          <view class="setting-content">
            <view class="setting-title">界面语言</view>
            <view class="setting-desc">{{ getLanguageText(settings.language) }}</view>
          </view>
          <view class="setting-arrow">›</view>
        </view>
      </view>
    </view>

    <!-- 数据管理 -->
    <view class="user-card">
      <view class="card-title">数据管理</view>
      <view class="settings-list">
        <view class="setting-item" @click="clearCache">
          <view class="setting-content">
            <view class="setting-title">清理缓存</view>
            <view class="setting-desc">释放存储空间，提升应用性能</view>
          </view>
          <view class="setting-value">{{ cacheSize }}</view>
        </view>
      </view>
    </view>

    <!-- 语言选择弹窗 -->
    <u-popup v-model="showLanguagePopup" mode="bottom" border-radius="20">
      <view class="selector-popup">
        <view class="popup-header">
          <view class="popup-title">选择语言</view>
          <view class="popup-close" @click="showLanguagePopup = false">✕</view>
        </view>
        <view class="selector-list">
          <view
            class="selector-item"
            :class="{ active: settings.language === 'zh' }"
            @click="selectLanguage('zh')"
          >
            <text class="selector-icon">中</text>
            <text class="selector-text">中文</text>
            <text class="selector-check" v-if="settings.language === 'zh'">✓</text>
          </view>
          <view
            class="selector-item"
            :class="{ active: settings.language === 'en' }"
            @click="selectLanguage('en')"
          >
            <text class="selector-icon">EN</text>
            <text class="selector-text">English</text>
            <text class="selector-check" v-if="settings.language === 'en'">✓</text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: 'UserSettings',
  data() {
    return {
      showLanguagePopup: false,
      cacheSize: '0 MB',
      settings: {
        language: 'zh' // 默认中文
      }
    }
  },

  onLoad() {
    this.initPage()
  },

  methods: {
    /**
     * 初始化页面
     */
    initPage() {
      this.loadSettings()
      this.calculateCacheSize()
    },

    /**
     * 加载设置
     */
    loadSettings() {
      try {
        const settings = uni.getStorageSync('appSettings')
        if (settings) {
          this.settings = { ...this.settings, ...settings }
        }
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    },

    /**
     * 保存设置
     */
    saveSettings() {
      try {
        uni.setStorageSync('appSettings', this.settings)
      } catch (error) {
        console.error('保存设置失败:', error)
      }
    },

    /**
     * 设置项变化处理
     */
    onSettingChange(key, value) {
      this.settings[key] = value
      this.saveSettings()

      // 特殊处理语言切换
      if (key === 'language') {
        this.applyLanguage(value)
      }
    },

    /**
     * 显示语言选择器
     */
    showLanguageSelector() {
      this.showLanguagePopup = true
    },

    /**
     * 选择语言
     */
    selectLanguage(language) {
      this.onSettingChange('language', language)
      this.showLanguagePopup = false

      // 显示切换成功提示
      uni.showToast({
        title: language === 'zh' ? '已切换到中文' : 'Switched to English',
        icon: 'success'
      })
    },

    /**
     * 获取语言文本
     */
    getLanguageText(language) {
      const languageMap = {
        zh: '中文',
        en: 'English'
      }
      return languageMap[language] || '中文'
    },

    /**
     * 应用语言设置
     */
    applyLanguage(language) {
      // 这里可以实现语言切换逻辑
      console.log('切换语言:', language)

      // 可以在这里设置全局语言状态
      // 例如：this.$store.commit('setLanguage', language)
    },

    /**
     * 计算缓存大小
     */
    calculateCacheSize() {
      try {
        // 获取本地存储的数据大小
        const storageInfo = uni.getStorageInfoSync()
        const sizeKB = storageInfo.currentSize || 0

        if (sizeKB < 1024) {
          this.cacheSize = `${sizeKB} KB`
        } else {
          const sizeMB = (sizeKB / 1024).toFixed(1)
          this.cacheSize = `${sizeMB} MB`
        }
      } catch (error) {
        console.error('计算缓存大小失败:', error)
        this.cacheSize = '未知'
      }
    },

    /**
     * 清理缓存
     */
    clearCache() {
      const currentLanguage = this.settings.language
      const title = currentLanguage === 'zh' ? '清理缓存' : 'Clear Cache'
      const content = currentLanguage === 'zh'
        ? '确定要清理应用缓存吗？这将删除临时文件和缓存数据。'
        : 'Are you sure you want to clear the app cache? This will delete temporary files and cached data.'
      const confirmText = currentLanguage === 'zh' ? '确定' : 'Confirm'
      const cancelText = currentLanguage === 'zh' ? '取消' : 'Cancel'

      uni.showModal({
        title: title,
        content: content,
        confirmText: confirmText,
        cancelText: cancelText,
        success: (res) => {
          if (res.confirm) {
            const loadingText = currentLanguage === 'zh' ? '清理中...' : 'Clearing...'
            const successText = currentLanguage === 'zh' ? '清理完成' : 'Cache cleared'

            uni.showLoading({
              title: loadingText
            })

            // 实际清理缓存操作
            setTimeout(() => {
              try {
                // 清理本地存储中的缓存数据（保留设置）
                const settings = uni.getStorageSync('appSettings')
                uni.clearStorageSync()
                if (settings) {
                  uni.setStorageSync('appSettings', settings)
                }

                uni.hideLoading()
                this.calculateCacheSize() // 重新计算缓存大小

                uni.showToast({
                  title: successText,
                  icon: 'success'
                })
              } catch (error) {
                uni.hideLoading()
                const errorText = currentLanguage === 'zh' ? '清理失败' : 'Clear failed'
                uni.showToast({
                  title: errorText,
                  icon: 'error'
                })
                console.error('清理缓存失败:', error)
              }
            }, 1500)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../styles/common.scss';

.card-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: $spacing-lg;
}

.settings-list {
  .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg 0;
    border-bottom: 1rpx solid $divider-color;
    transition: background-color $transition-fast;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &.danger {
      .setting-title {
        color: $error-color;
      }
    }

    .setting-content {
      flex: 1;

      .setting-title {
        font-size: $font-size-md;
        color: $text-primary;
        margin-bottom: $spacing-xs;
      }

      .setting-desc {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }

    .setting-arrow {
      color: $text-light;
      font-size: $font-size-lg;
    }

    .setting-value {
      font-size: $font-size-sm;
      color: $text-secondary;
      margin-right: $spacing-sm;
    }
  }
}

.selector-popup {
  background: $white;
  border-radius: 20rpx 20rpx 0 0;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg;
    border-bottom: 1rpx solid $divider-color;

    .popup-title {
      font-size: $font-size-lg;
      font-weight: 600;
      color: $text-primary;
    }

    .popup-close {
      font-size: $font-size-lg;
      color: $text-light;
      padding: $spacing-sm;
    }
  }

  .selector-list {
    .selector-item {
      display: flex;
      align-items: center;
      padding: $spacing-lg;
      border-bottom: 1rpx solid $divider-color;
      transition: background-color $transition-fast;

      &:last-child {
        border-bottom: none;
      }

      &.active {
        background-color: $primary-light;
      }

      &:active {
        background-color: rgba(0, 0, 0, 0.02);
      }

      .selector-icon {
        font-size: $font-size-lg;
        margin-right: $spacing-md;
      }

      .selector-text {
        flex: 1;
        font-size: $font-size-md;
        color: $text-primary;

        &.small-font {
          font-size: $font-size-sm;
        }

        &.medium-font {
          font-size: $font-size-md;
        }

        &.large-font {
          font-size: $font-size-lg;
        }
      }

      .selector-check {
        font-size: $font-size-lg;
        color: $primary-color;
      }
    }
  }
}
</style>
