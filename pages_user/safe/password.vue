<!--
 * @Description: 修改密码页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02
 * @Layout: 密码修改表单
-->
<template>
  <view class="password-page">

    <!-- 修改密码表单 -->
    <view class="form-container">
      <view class="form-item">
        <view class="form-label">当前密码</view>
        <input
          class="form-input"
          type="password"
          placeholder="请输入当前密码"
          v-model="formData.oldPassword"
          :password="!showOldPassword"
        />
        <view class="password-toggle" @click="toggleOldPassword">
          <text>{{ showOldPassword ? '👁️' : '👁️‍🗨️' }}</text>
        </view>
      </view>

      <view class="form-item">
        <view class="form-label">新密码</view>
        <input
          class="form-input"
          type="password"
          placeholder="请输入新密码"
          v-model="formData.newPassword"
          :password="!showNewPassword"
        />
        <view class="password-toggle" @click="toggleNewPassword">
          <text>{{ showNewPassword ? '👁️' : '👁️‍🗨️' }}</text>
        </view>
      </view>

      <view class="form-item">
        <view class="form-label">确认新密码</view>
        <input
          class="form-input"
          type="password"
          placeholder="请再次输入新密码"
          v-model="formData.confirmPassword"
          :password="!showConfirmPassword"
        />
        <view class="password-toggle" @click="toggleConfirmPassword">
          <text>{{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}</text>
        </view>
      </view>

      <!-- 密码强度提示 -->
      <view class="password-strength">
        <view class="strength-title">密码强度：</view>
        <view class="strength-bar">
          <view
            class="strength-item"
            :class="{ 'active': passwordStrength >= 1, 'weak': passwordStrength === 1 }"
          ></view>
          <view
            class="strength-item"
            :class="{ 'active': passwordStrength >= 2, 'medium': passwordStrength === 2 }"
          ></view>
          <view
            class="strength-item"
            :class="{ 'active': passwordStrength >= 3, 'strong': passwordStrength === 3 }"
          ></view>
        </view>
        <view class="strength-text">{{ strengthText }}</view>
      </view>

      <!-- 密码要求 -->
      <view class="password-requirements">
        <view class="requirements-title">密码要求：</view>
        <view class="requirement-item" :class="{ 'valid': requirements.length }">
          <text class="requirement-icon">{{ requirements.length ? '✓' : '○' }}</text>
          <text class="requirement-text">长度8-20位</text>
        </view>
        <view class="requirement-item" :class="{ 'valid': requirements.letter }">
          <text class="requirement-icon">{{ requirements.letter ? '✓' : '○' }}</text>
          <text class="requirement-text">包含字母</text>
        </view>
        <view class="requirement-item" :class="{ 'valid': requirements.number }">
          <text class="requirement-icon">{{ requirements.number ? '✓' : '○' }}</text>
          <text class="requirement-text">包含数字</text>
        </view>
        <view class="requirement-item" :class="{ 'valid': requirements.special }">
          <text class="requirement-icon">{{ requirements.special ? '✓' : '○' }}</text>
          <text class="requirement-text">包含特殊字符（可选）</text>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-container p-footer">
        <button
          class="submit-btn"
          :class="{ 'disabled': !canSubmit }"
          @click="handleSubmit"
          :disabled="!canSubmit"
        >
          确认修改
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SafePassword',
  data() {
    return {
      formData: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      showOldPassword: false,
      showNewPassword: false,
      showConfirmPassword: false
    }
  },

  computed: {
    /**
     * 密码强度
     */
    passwordStrength() {
      const password = this.formData.newPassword
      if (!password) return 0

      let strength = 0
      if (password.length >= 8) strength++
      if (/[a-zA-Z]/.test(password) && /[0-9]/.test(password)) strength++
      if (/[^a-zA-Z0-9]/.test(password)) strength++

      return strength
    },

    /**
     * 强度文本
     */
    strengthText() {
      switch (this.passwordStrength) {
        case 0: return '无'
        case 1: return '弱'
        case 2: return '中'
        case 3: return '强'
        default: return '无'
      }
    },

    /**
     * 密码要求检查
     */
    requirements() {
      const password = this.formData.newPassword
      return {
        length: password.length >= 8 && password.length <= 20,
        letter: /[a-zA-Z]/.test(password),
        number: /[0-9]/.test(password),
        special: /[^a-zA-Z0-9]/.test(password)
      }
    },

    /**
     * 是否可以提交
     */
    canSubmit() {
      return this.formData.oldPassword &&
             this.formData.newPassword &&
             this.formData.confirmPassword &&
             this.formData.newPassword === this.formData.confirmPassword &&
             this.requirements.length &&
             this.requirements.letter &&
             this.requirements.number
    }
  },

  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 切换旧密码显示
     */
    toggleOldPassword() {
      this.showOldPassword = !this.showOldPassword
    },

    /**
     * 切换新密码显示
     */
    toggleNewPassword() {
      this.showNewPassword = !this.showNewPassword
    },

    /**
     * 切换确认密码显示
     */
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword
    },

    /**
     * 处理提交
     */
    handleSubmit() {
      if (!this.canSubmit) return

      if (this.formData.newPassword !== this.formData.confirmPassword) {
        uni.showToast({
          title: '两次密码输入不一致',
          icon: 'none'
        })
        return
      }

      // 这里应该调用API修改密码
      this.changePassword()
    },

    /**
     * 修改密码
     */
    async changePassword() {
      try {
        uni.showLoading({
          title: '修改中...'
        })

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1500))

        uni.hideLoading()
        uni.showToast({
          title: '密码修改成功',
          icon: 'success'
        })

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '修改失败，请重试',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.password-page {
  min-height: 100vh;
  background: #f5f5f5;
}


/* 表单容器 */
.form-container {
  padding: 30rpx;
}

.form-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  font-size: 30rpx;
  color: #333;
  padding-right: 80rpx;
}

.password-toggle {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

/* 密码强度 */
.password-strength {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.strength-title {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.strength-bar {
  display: flex;
  gap: 8rpx;
  margin-right: 20rpx;
}

.strength-item {
  width: 60rpx;
  height: 8rpx;
  background: #e9ecef;
  border-radius: 4rpx;
}

.strength-item.active.weak {
  background: #ff6b6b;
}

.strength-item.active.medium {
  background: #ffa726;
}

.strength-item.active.strong {
  background: #51cf66;
}

.strength-text {
  font-size: 26rpx;
  color: #666;
}

/* 密码要求 */
.password-requirements {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.requirements-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.requirement-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.requirement-item:last-child {
  margin-bottom: 0;
}

.requirement-icon {
  width: 40rpx;
  font-size: 24rpx;
  color: #ccc;
  margin-right: 12rpx;
}

.requirement-item.valid .requirement-icon {
  color: #51cf66;
}

.requirement-text {
  font-size: 26rpx;
  color: #666;
}

.requirement-item.valid .requirement-text {
  color: #333;
}

/* 提交按钮 */
.submit-container {
  padding: 20rpx 0;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn.disabled {
  background: #e9ecef;
  color: #adb5bd;
}
</style>
