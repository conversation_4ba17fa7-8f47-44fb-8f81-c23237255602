<!--
 * @Description: 关于我们页面 - 简洁的应用介绍
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-01
 * @Layout: 简单的应用介绍，白色背景
-->
<template>
  <view class="about-page">
    <!-- 顶部大logo -->
    <view class="app-header">
      <image class="app-logo" src="/static/images/ai.gif" mode="aspectFit" />
      <view class="app-name">APP小蓝小助理</view>
      <view class="app-description">
        一款简洁实用的生活助手应用，帮助您更好地管理日常生活。
      </view>
    </view>

    <!-- 主要功能 -->
    <view class="features-section">
      <view class="section-title">主要功能</view>
      <view class="features-grid">
        <view class="feature-item" v-for="(feature, index) in features" :key="index">
          <view class="feature-icon">{{ feature.icon }}</view>
          <view class="feature-name">{{ feature.name }}</view>
        </view>
      </view>
    </view>

    <!-- 版权信息 -->
    <view class="copyright">
      <text>© 2025 小助理</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserAbout',
  data() {
    return {
      features: [
        {
          icon: '✅',
          name: '待办任务'
        },
        {
          icon: '💭',
          name: '记忆管理'
        },
        {
          icon: '💰',
          name: '记账理财'
        },
        {
          icon: '💬',
          name: 'AI对话'
        },
        {
          icon: '📚',
          name: '知识库'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.about-page {
  min-height: calc(100vh - 100rpx);
  background-color: #ffffff;
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
}

.app-header {
  text-align: center;
  padding: 30rpx 0 80rpx;

  .app-logo {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
    border-radius: 30rpx;
  }

  .app-name {
    font-size: 46rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 30rpx;
  }

  .app-description {
    font-size: 30rpx;
    color: #666666;
    line-height: 1.6;
    padding: 0 40rpx;
  }
}

.features-section {
  flex: 1;
  margin-bottom: 80rpx;

  .section-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 50rpx;
    text-align: center;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40rpx;
    padding: 0 20rpx;
  }

  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .feature-icon {
      font-size: 60rpx;
      margin-bottom: 20rpx;
    }

    .feature-name {
      font-size: 26rpx;
      color: #333333;
      font-weight: 500;
    }
  }
}

.copyright {
  text-align: center;
  padding-top: 40rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: auto;

  text {
    font-size: 24rpx;
    color: #999999;
  }
}
</style>
