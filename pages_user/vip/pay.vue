<!--
 * @Description: VIP支付历史记录页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02 14:39:11
 * @Features: 支付记录列表、订单详情、状态筛选
-->

<template>
	<view class="pay-history-page">
		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-tabs">
				<view
					class="filter-tab"
					:class="{ 'active': currentFilter === item.value }"
					v-for="item in filterTabs"
					:key="item.value"
					@click="changeFilter(item.value)"
				>
					{{ item.label }}
				</view>
			</view>
		</view>

		<!-- 支付记录列表 -->
		<view class="pay-list" v-if="payList.length > 0">
			<view
				class="pay-item"
				v-for="(item, index) in filteredPayList"
				:key="index"
				@click="showPayDetail(item)"
			>
				<view class="pay-header">
					<view class="pay-title">{{ item.title }}</view>
					<view class="pay-status" :class="'status-' + item.status">
						{{ getStatusText(item.status) }}
					</view>
				</view>

				<view class="pay-info">
					<view class="pay-amount">¥{{ item.amount }}</view>
					<view class="pay-time">{{ formatTime(item.createTime) }}</view>
				</view>

				<view class="pay-detail">
					<view class="order-no">订单号：{{ item.orderNo }}</view>
					<view class="pay-method">{{ item.payMethod }}</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<view class="empty-icon">📋</view>
			<view class="empty-text">暂无支付记录</view>
			<view class="empty-desc">您还没有任何VIP购买记录</view>
			<u-button
				type="primary"
				size="normal"
				:custom-style="goVipButtonStyle"
				@click="goToVip"
			>
				立即开通VIP
			</u-button>
		</view>

		<!-- 支付详情弹窗 -->
		<u-popup
			v-model="showDetailPopup"
			mode="bottom"
			border-radius="24"
			:safe-area-inset-bottom="true"
		>
			<view class="detail-popup" v-if="selectedPayItem">
				<view class="popup-header">
					<view class="popup-title">支付详情</view>
					<view class="popup-close" @click="closeDetailPopup">
						<u-icon name="close" size="20" color="#999"></u-icon>
					</view>
				</view>

				<view class="detail-content">
					<view class="detail-row">
						<text class="detail-label">商品名称</text>
						<text class="detail-value">{{ selectedPayItem.title }}</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">支付金额</text>
						<text class="detail-value amount">¥{{ selectedPayItem.amount }}</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">订单号</text>
						<text class="detail-value">{{ selectedPayItem.orderNo }}</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">支付方式</text>
						<text class="detail-value">{{ selectedPayItem.payMethod }}</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">创建时间</text>
						<text class="detail-value">{{ formatDetailTime(selectedPayItem.createTime) }}</text>
					</view>
					<view class="detail-row" v-if="selectedPayItem.payTime">
						<text class="detail-label">支付时间</text>
						<text class="detail-value">{{ formatDetailTime(selectedPayItem.payTime) }}</text>
					</view>
					<view class="detail-row">
						<text class="detail-label">订单状态</text>
						<text class="detail-value" :class="'status-' + selectedPayItem.status">
							{{ getStatusText(selectedPayItem.status) }}
						</text>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
/**
 * VIP支付历史记录页面逻辑
 * 功能：支付记录展示、状态筛选、订单详情
 */
export default {
	name: "VipPayHistory",
	data() {
		return {
			// 当前筛选条件
			currentFilter: 'all',

			// 筛选选项
			filterTabs: [
				{ label: '全部', value: 'all' },
				{ label: '已支付', value: 'paid' },
				{ label: '待支付', value: 'pending' },
				{ label: '已取消', value: 'cancelled' }
			],

			// 支付记录列表（模拟数据）
			payList: [
				{
					id: '1',
					title: 'VIP会员 - 1个月',
					amount: '29.00',
					orderNo: 'VIP202501020001',
					payMethod: '微信支付',
					status: 'paid',
					createTime: '2025-01-02 14:30:00',
					payTime: '2025-01-02 14:31:25'
				},
				{
					id: '2',
					title: 'VIP会员 - 3个月',
					amount: '79.00',
					orderNo: 'VIP202412250002',
					payMethod: '支付宝',
					status: 'paid',
					createTime: '2024-12-25 10:15:30',
					payTime: '2024-12-25 10:16:45'
				},
				{
					id: '3',
					title: 'VIP会员 - 1个月',
					amount: '29.00',
					orderNo: 'VIP202412200003',
					payMethod: '微信支付',
					status: 'cancelled',
					createTime: '2024-12-20 16:20:00',
					payTime: null
				}
			],

			// 详情弹窗
			showDetailPopup: false,
			selectedPayItem: null,

			// 按钮样式
			goVipButtonStyle: {
				borderRadius: '8rpx',
				height: '72rpx',
				fontSize: '28rpx',
				fontWeight: '600',
				marginTop: '32rpx'
			}
		};
	},

	computed: {
		// 根据筛选条件过滤支付记录
		filteredPayList() {
			if (this.currentFilter === 'all') {
				return this.payList;
			}
			return this.payList.filter(item => item.status === this.currentFilter);
		}
	},

	onLoad() {
		// 页面加载时获取支付记录
		this.loadPayHistory();
	},

	onPullDownRefresh() {
		// 下拉刷新
		this.loadPayHistory().then(() => {
			uni.stopPullDownRefresh();
		});
	},

	methods: {
		/**
		 * 切换筛选条件
		 */
		changeFilter(filterValue) {
			this.currentFilter = filterValue;
		},

		/**
		 * 显示支付详情
		 */
		showPayDetail(payItem) {
			this.selectedPayItem = payItem;
			this.showDetailPopup = true;
		},

		/**
		 * 关闭详情弹窗
		 */
		closeDetailPopup() {
			this.showDetailPopup = false;
			this.selectedPayItem = null;
		},

		/**
		 * 跳转到VIP页面
		 */
		goToVip() {
			uni.navigateBack({
				delta: 1
			});
		},

		/**
		 * 获取状态文本
		 */
		getStatusText(status) {
			const statusMap = {
				'paid': '已支付',
				'pending': '待支付',
				'cancelled': '已取消'
			};
			return statusMap[status] || '未知';
		},

		/**
		 * 格式化时间（列表显示）
		 */
		formatTime(timeStr) {
			if (!timeStr) return '';

			const date = new Date(timeStr);
			const now = new Date();
			const diff = now.getTime() - date.getTime();
			const days = Math.floor(diff / (1000 * 60 * 60 * 24));

			if (days === 0) {
				return '今天 ' + timeStr.split(' ')[1].substring(0, 5);
			} else if (days === 1) {
				return '昨天 ' + timeStr.split(' ')[1].substring(0, 5);
			} else if (days < 7) {
				return days + '天前';
			} else {
				return timeStr.split(' ')[0];
			}
		},

		/**
		 * 格式化时间（详情显示）
		 */
		formatDetailTime(timeStr) {
			if (!timeStr) return '';
			return timeStr;
		},

		/**
		 * 加载支付历史记录
		 * 实际项目中应该调用API获取真实数据
		 */
		loadPayHistory() {
			return new Promise((resolve) => {
				// 模拟API请求
				setTimeout(() => {
					// 这里应该替换为真实的API调用
					// this.payList = response.data;
					resolve();
				}, 500);
			});
		}
	}
};
</script>

<style lang="scss" scoped>
// Ant Design 主色调
$primary-color: #1890ff;
$primary-light: #e6f7ff;
$primary-dark: #096dd9;

// 基础色彩
$white: #ffffff;
$text-primary: #262626;
$text-secondary: #8c8c8c;
$text-light: #bfbfbf;
$border-color: #f0f0f0;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;

// 间距
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-md: 16rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;

// 圆角
$border-radius-sm: 4rpx;
$border-radius-md: 8rpx;
$border-radius-lg: 12rpx;
$border-radius-xl: 16rpx;

// 字体大小
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-md: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;

.pay-history-page {
	background-color: $white;
	min-height: 100vh;
}

// 筛选栏
.filter-bar {
	background-color: $white;
	padding: $spacing-lg;
	border-bottom: 1rpx solid $border-color;

	.filter-tabs {
		display: flex;
		background-color: #f8f9fa;
		border-radius: $border-radius-md;
		padding: 4rpx;

		.filter-tab {
			flex: 1;
			text-align: center;
			padding: $spacing-sm $spacing-md;
			font-size: $font-size-sm;
			color: $text-secondary;
			border-radius: $border-radius-sm;
			transition: all 0.3s ease;

			&.active {
				background-color: $primary-color;
				color: $white;
				font-weight: 600;
			}
		}
	}
}

// 支付记录列表
.pay-list {
	padding: $spacing-lg;

	.pay-item {
		background-color: $white;
		border-radius: $border-radius-lg;
		padding: $spacing-lg;
		margin-bottom: $spacing-md;
		border: 1rpx solid $border-color;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			background-color: #f8f9fa;
		}

		&:last-child {
			margin-bottom: 0;
		}

		.pay-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: $spacing-md;

			.pay-title {
				font-size: $font-size-md;
				font-weight: 600;
				color: $text-primary;
			}

			.pay-status {
				font-size: $font-size-xs;
				padding: 4rpx 12rpx;
				border-radius: $border-radius-sm;
				font-weight: 600;

				&.status-paid {
					background-color: #f6ffed;
					color: $success-color;
				}

				&.status-pending {
					background-color: #fff7e6;
					color: $warning-color;
				}

				&.status-cancelled {
					background-color: #fff2f0;
					color: $error-color;
				}
			}
		}

		.pay-info {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: $spacing-sm;

			.pay-amount {
				font-size: $font-size-lg;
				font-weight: 700;
				color: $primary-color;
			}

			.pay-time {
				font-size: $font-size-sm;
				color: $text-secondary;
			}
		}

		.pay-detail {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.order-no {
				font-size: $font-size-xs;
				color: $text-light;
			}

			.pay-method {
				font-size: $font-size-xs;
				color: $text-secondary;
			}
		}
	}
}

// 空状态
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx $spacing-lg;

	.empty-icon {
		font-size: 120rpx;
		margin-bottom: $spacing-lg;
		opacity: 0.6;
	}

	.empty-text {
		font-size: $font-size-lg;
		font-weight: 600;
		color: $text-primary;
		margin-bottom: $spacing-sm;
	}

	.empty-desc {
		font-size: $font-size-sm;
		color: $text-secondary;
		text-align: center;
		line-height: 1.5;
	}
}

// 详情弹窗
.detail-popup {
	background-color: $white;
	border-radius: 24rpx 24rpx 0 0;
	max-height: 80vh;

	.popup-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: $spacing-lg;
		border-bottom: 1rpx solid $border-color;

		.popup-title {
			font-size: $font-size-lg;
			font-weight: 600;
			color: $text-primary;
		}

		.popup-close {
			padding: $spacing-sm;
		}
	}

	.detail-content {
		padding: $spacing-lg;

		.detail-row {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: $spacing-md 0;
			border-bottom: 1rpx solid #fafafa;

			&:last-child {
				border-bottom: none;
			}

			.detail-label {
				font-size: $font-size-sm;
				color: $text-secondary;
			}

			.detail-value {
				font-size: $font-size-sm;
				color: $text-primary;
				text-align: right;
				max-width: 60%;
				word-break: break-all;

				&.amount {
					font-size: $font-size-md;
					font-weight: 600;
					color: $primary-color;
				}

				&.status-paid {
					color: $success-color;
				}

				&.status-pending {
					color: $warning-color;
				}

				&.status-cancelled {
					color: $error-color;
				}
			}
		}
	}
}
</style>
